"use client"

import { useState, useMemo } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Calendar,
  User,
  ArrowRight,
  Search,
  TrendingUp,
  BookOpen,
  Clock,
  Eye,
  Heart,
  MessageCircle,
  Filter,
  Grid,
  List,
  Star,
  Award,
  Lightbulb,
  Home,
  Palette,
} from "lucide-react"
import Header from "@/components/header"

export default function BlogPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [sortBy, setSortBy] = useState("latest")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  const blogPosts = [
    {
      id: 1,
      title: "10 Must-Have Tech Gadgets for Your Smart Home in 2024",
      excerpt:
        "Discover the latest technology trends and smart home gadgets that will revolutionize your daily life this year. From AI-powered assistants to automated lighting systems.",
      content: "Transform your living space with cutting-edge technology...",
      image: "/placeholder.svg?height=400&width=600",
      author: {
        name: "<PERSON>",
        avatar: "/placeholder.svg?height=40&width=40",
        bio: "Tech enthusiast and smart home expert",
      },
      date: "2024-01-15",
      category: "Technology",
      tags: ["Smart Home", "Gadgets", "IoT", "Automation"],
      readTime: "5 min read",
      views: 2847,
      likes: 156,
      comments: 23,
      featured: true,
      trending: true,
    },
    {
      id: 2,
      title: "Sustainable Fashion: Building an Eco-Friendly Wardrobe That Lasts",
      excerpt:
        "Learn how to make conscious fashion choices that benefit both you and the environment. Discover sustainable brands and timeless pieces.",
      content: "Creating a sustainable wardrobe starts with understanding...",
      image: "/placeholder.svg?height=400&width=600",
      author: {
        name: "Emily Chen",
        avatar: "/placeholder.svg?height=40&width=40",
        bio: "Sustainable fashion advocate",
      },
      date: "2024-01-12",
      category: "Fashion",
      tags: ["Sustainability", "Eco-Friendly", "Wardrobe", "Style"],
      readTime: "7 min read",
      views: 1923,
      likes: 89,
      comments: 15,
      featured: false,
      trending: true,
    },
    {
      id: 3,
      title: "Home Office Setup Guide: Creating the Perfect Productive Workspace",
      excerpt:
        "Create the perfect workspace at home with our comprehensive guide to office furniture, lighting, and productivity accessories.",
      content: "Working from home requires a dedicated space that promotes...",
      image: "/placeholder.svg?height=400&width=600",
      author: {
        name: "Mike Davis",
        avatar: "/placeholder.svg?height=40&width=40",
        bio: "Interior design consultant",
      },
      date: "2024-01-10",
      category: "Home & Office",
      tags: ["Home Office", "Productivity", "Furniture", "Design"],
      readTime: "6 min read",
      views: 3156,
      likes: 234,
      comments: 41,
      featured: true,
      trending: false,
    },
    {
      id: 4,
      title: "Fitness Equipment for Small Spaces: Maximize Your Home Gym",
      excerpt:
        "Stay fit at home with compact and versatile fitness equipment that doesn't take up much space but delivers maximum results.",
      content: "Small spaces don't mean you have to compromise on fitness...",
      image: "/placeholder.svg?height=400&width=600",
      author: {
        name: "Alex Rodriguez",
        avatar: "/placeholder.svg?height=40&width=40",
        bio: "Fitness trainer and wellness coach",
      },
      date: "2024-01-08",
      category: "Fitness",
      tags: ["Fitness", "Home Gym", "Small Spaces", "Equipment"],
      readTime: "4 min read",
      views: 1567,
      likes: 78,
      comments: 12,
      featured: false,
      trending: false,
    },
    {
      id: 5,
      title: "Kitchen Essentials for Beginner Cooks: Start Your Culinary Journey",
      excerpt:
        "Start your culinary journey with these essential kitchen tools and appliances that every beginner cook needs to succeed.",
      content: "Cooking at home can be intimidating for beginners...",
      image: "/placeholder.svg?height=400&width=600",
      author: {
        name: "Lisa Thompson",
        avatar: "/placeholder.svg?height=40&width=40",
        bio: "Professional chef and cookbook author",
      },
      date: "2024-01-05",
      category: "Kitchen",
      tags: ["Cooking", "Kitchen Tools", "Beginner", "Essentials"],
      readTime: "8 min read",
      views: 2234,
      likes: 145,
      comments: 28,
      featured: false,
      trending: true,
    },
    {
      id: 6,
      title: "Smart Home Automation on a Budget: Affordable Tech Solutions",
      excerpt:
        "Transform your home into a smart home without breaking the bank with these affordable automation solutions and DIY tips.",
      content: "Smart home technology doesn't have to be expensive...",
      image: "/placeholder.svg?height=400&width=600",
      author: {
        name: "David Wilson",
        avatar: "/placeholder.svg?height=40&width=40",
        bio: "DIY enthusiast and tech blogger",
      },
      date: "2024-01-03",
      category: "Technology",
      tags: ["Smart Home", "Budget", "DIY", "Automation"],
      readTime: "6 min read",
      views: 1876,
      likes: 92,
      comments: 19,
      featured: false,
      trending: false,
    },
    {
      id: 7,
      title: "Interior Design Trends 2024: What's Hot This Year",
      excerpt:
        "Explore the hottest interior design trends for 2024, from color palettes to furniture styles that will define modern living.",
      content: "This year's design trends focus on sustainability...",
      image: "/placeholder.svg?height=400&width=600",
      author: {
        name: "Rachel Green",
        avatar: "/placeholder.svg?height=40&width=40",
        bio: "Interior designer and trend forecaster",
      },
      date: "2024-01-01",
      category: "Design",
      tags: ["Interior Design", "Trends", "2024", "Home Decor"],
      readTime: "9 min read",
      views: 4521,
      likes: 312,
      comments: 67,
      featured: true,
      trending: true,
    },
    {
      id: 8,
      title: "Minimalist Living: How to Declutter Your Home and Mind",
      excerpt:
        "Discover the art of minimalist living and learn practical tips for decluttering your space to create a more peaceful environment.",
      content: "Minimalism is more than just a design aesthetic...",
      image: "/placeholder.svg?height=400&width=600",
      author: {
        name: "James Park",
        avatar: "/placeholder.svg?height=40&width=40",
        bio: "Minimalism coach and lifestyle blogger",
      },
      date: "2023-12-28",
      category: "Lifestyle",
      tags: ["Minimalism", "Decluttering", "Lifestyle", "Wellness"],
      readTime: "7 min read",
      views: 2987,
      likes: 198,
      comments: 34,
      featured: false,
      trending: false,
    },
  ]

  const categories = [
    { id: "all", name: "All Posts", icon: BookOpen, count: blogPosts.length },
    {
      id: "Technology",
      name: "Technology",
      icon: Lightbulb,
      count: blogPosts.filter((p) => p.category === "Technology").length,
    },
    { id: "Fashion", name: "Fashion", icon: Star, count: blogPosts.filter((p) => p.category === "Fashion").length },
    {
      id: "Home & Office",
      name: "Home & Office",
      icon: Home,
      count: blogPosts.filter((p) => p.category === "Home & Office").length,
    },
    { id: "Fitness", name: "Fitness", icon: Award, count: blogPosts.filter((p) => p.category === "Fitness").length },
    { id: "Kitchen", name: "Kitchen", icon: User, count: blogPosts.filter((p) => p.category === "Kitchen").length },
    { id: "Design", name: "Design", icon: Palette, count: blogPosts.filter((p) => p.category === "Design").length },
    {
      id: "Lifestyle",
      name: "Lifestyle",
      icon: Heart,
      count: blogPosts.filter((p) => p.category === "Lifestyle").length,
    },
  ]

  // Filter and sort posts
  const filteredPosts = useMemo(() => {
    const filtered = blogPosts.filter((post) => {
      if (
        searchQuery &&
        !post.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !post.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      ) {
        return false
      }
      if (selectedCategory !== "all" && post.category !== selectedCategory) {
        return false
      }
      return true
    })

    // Sort posts
    switch (sortBy) {
      case "popular":
        filtered.sort((a, b) => b.views - a.views)
        break
      case "trending":
        filtered.sort((a, b) => (b.trending ? 1 : 0) - (a.trending ? 1 : 0))
        break
      case "oldest":
        filtered.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        break
      default: // latest
        filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    }

    return filtered
  }, [searchQuery, selectedCategory, sortBy])

  const featuredPosts = blogPosts.filter((post) => post.featured)
  const trendingPosts = blogPosts.filter((post) => post.trending).slice(0, 5)
  const popularTags = ["Smart Home", "Sustainability", "Design", "Productivity", "Fitness", "Cooking", "DIY", "Trends"]

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">FurnitureStore Blog</h1>
          <p className="text-xl opacity-90 max-w-3xl mx-auto mb-8">
            Discover the latest trends, tips, and insights about furniture, lifestyle, technology, and home design from
            our expert writers
          </p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder="Search articles, topics, or tags..."
              className="pl-12 pr-4 py-4 text-lg bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder:text-white/70"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        {/* Featured Posts Section */}
        {featuredPosts.length > 0 && (
          <section className="mb-16">
            <div className="flex items-center gap-3 mb-8">
              <Star className="h-6 w-6 text-yellow-500" />
              <h2 className="text-3xl font-bold">Featured Articles</h2>
            </div>

            <div className="grid lg:grid-cols-2 gap-8">
              {/* Main Featured Post */}
              <Card className="overflow-hidden group hover:shadow-xl transition-all duration-300">
                <div className="relative h-80">
                  <Image
                    src={featuredPosts[0]?.image || "/placeholder.svg"}
                    alt={featuredPosts[0]?.title || ""}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <Badge className="absolute top-4 left-4 bg-yellow-500 text-black">Featured</Badge>
                  {featuredPosts[0]?.trending && (
                    <Badge className="absolute top-4 right-4 bg-red-500 text-white">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      Trending
                    </Badge>
                  )}
                  <div className="absolute bottom-6 left-6 right-6 text-white">
                    <Badge className="mb-3 bg-blue-600">{featuredPosts[0]?.category}</Badge>
                    <h3 className="text-2xl font-bold mb-3 line-clamp-2">{featuredPosts[0]?.title}</h3>
                    <p className="text-sm opacity-90 line-clamp-2 mb-4">{featuredPosts[0]?.excerpt}</p>
                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={featuredPosts[0]?.author.avatar || "/placeholder.svg"} />
                          <AvatarFallback>{featuredPosts[0]?.author.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <span>{featuredPosts[0]?.author.name}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>{featuredPosts[0]?.date}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{featuredPosts[0]?.readTime}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Secondary Featured Posts */}
              <div className="space-y-6">
                {featuredPosts.slice(1, 3).map((post) => (
                  <Card key={post.id} className="overflow-hidden group hover:shadow-lg transition-shadow">
                    <div className="flex gap-4 p-4">
                      <div className="relative w-32 h-24 flex-shrink-0">
                        <Image
                          src={post.image || "/placeholder.svg"}
                          alt={post.title}
                          fill
                          className="object-cover rounded-lg"
                        />
                      </div>
                      <div className="flex-1">
                        <Badge className="mb-2 text-xs">{post.category}</Badge>
                        <h4 className="font-semibold mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                          {post.title}
                        </h4>
                        <div className="flex items-center gap-3 text-xs text-gray-600">
                          <span>{post.author.name}</span>
                          <span>{post.date}</span>
                          <span>{post.readTime}</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </section>
        )}

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Filter and Sort Bar */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
              <div>
                <h2 className="text-2xl font-bold mb-2">
                  {selectedCategory === "all" ? "All Articles" : `${selectedCategory} Articles`}
                </h2>
                <p className="text-gray-600">Showing {filteredPosts.length} articles</p>
              </div>

              <div className="flex items-center gap-4">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        <div className="flex items-center gap-2">
                          <category.icon className="h-4 w-4" />
                          {category.name} ({category.count})
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="latest">Latest</SelectItem>
                    <SelectItem value="popular">Most Popular</SelectItem>
                    <SelectItem value="trending">Trending</SelectItem>
                    <SelectItem value="oldest">Oldest</SelectItem>
                  </SelectContent>
                </Select>

                <div className="flex border rounded-md">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Blog Posts Grid/List */}
            <div className={viewMode === "grid" ? "grid md:grid-cols-2 gap-8" : "space-y-8"}>
              {filteredPosts.map((post) => (
                <Card key={post.id} className="overflow-hidden group hover:shadow-xl transition-all duration-300">
                  {viewMode === "grid" ? (
                    <>
                      <div className="relative h-48">
                        <Image
                          src={post.image || "/placeholder.svg"}
                          alt={post.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <Badge className="absolute top-4 left-4">{post.category}</Badge>
                        {post.trending && (
                          <Badge className="absolute top-4 right-4 bg-red-500 text-white">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            Trending
                          </Badge>
                        )}
                      </div>
                      <CardContent className="p-6">
                        <div className="flex flex-wrap gap-2 mb-3">
                          {post.tags.slice(0, 2).map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        <h3 className="text-xl font-semibold mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors">
                          {post.title}
                        </h3>
                        <p className="text-gray-600 mb-4 line-clamp-3">{post.excerpt}</p>

                        <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={post.author.avatar || "/placeholder.svg"} />
                              <AvatarFallback>{post.author.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <span>{post.author.name}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            <span>{post.date}</span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              <span>{post.readTime}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Eye className="h-4 w-4" />
                              <span>{post.views.toLocaleString()}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Heart className="h-4 w-4" />
                              <span>{post.likes}</span>
                            </div>
                          </div>
                          <Button variant="outline" size="sm">
                            Read More
                            <ArrowRight className="h-4 w-4 ml-1" />
                          </Button>
                        </div>
                      </CardContent>
                    </>
                  ) : (
                    <div className="flex gap-6 p-6">
                      <div className="relative w-48 h-32 flex-shrink-0">
                        <Image
                          src={post.image || "/placeholder.svg"}
                          alt={post.title}
                          fill
                          className="object-cover rounded-lg"
                        />
                        <Badge className="absolute top-2 left-2 text-xs">{post.category}</Badge>
                      </div>
                      <div className="flex-1">
                        <div className="flex flex-wrap gap-2 mb-3">
                          {post.tags.slice(0, 3).map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        <h3 className="text-xl font-semibold mb-3 group-hover:text-blue-600 transition-colors">
                          {post.title}
                        </h3>
                        <p className="text-gray-600 mb-4 line-clamp-2">{post.excerpt}</p>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={post.author.avatar || "/placeholder.svg"} />
                                <AvatarFallback>{post.author.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <span>{post.author.name}</span>
                            </div>
                            <span>{post.date}</span>
                            <span>{post.readTime}</span>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-3 text-sm text-gray-500">
                              <div className="flex items-center gap-1">
                                <Eye className="h-4 w-4" />
                                <span>{post.views.toLocaleString()}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Heart className="h-4 w-4" />
                                <span>{post.likes}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <MessageCircle className="h-4 w-4" />
                                <span>{post.comments}</span>
                              </div>
                            </div>
                            <Button variant="outline" size="sm">
                              Read More
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </Card>
              ))}
            </div>

            {filteredPosts.length === 0 && (
              <div className="text-center py-12">
                <BookOpen className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold mb-2">No articles found</h3>
                <p className="text-gray-600">Try adjusting your search or filter criteria</p>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Trending Posts */}
            <Card>
              <CardHeader>
                <h3 className="font-semibold flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-red-500" />
                  Trending Now
                </h3>
              </CardHeader>
              <CardContent className="space-y-4">
                {trendingPosts.map((post, index) => (
                  <div key={post.id} className="flex gap-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-sm line-clamp-2 hover:text-blue-600 cursor-pointer">
                        {post.title}
                      </h4>
                      <div className="flex items-center gap-2 text-xs text-gray-500 mt-1">
                        <Eye className="h-3 w-3" />
                        <span>{post.views.toLocaleString()}</span>
                        <span>•</span>
                        <span>{post.readTime}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Categories */}
            <Card>
              <CardHeader>
                <h3 className="font-semibold flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Categories
                </h3>
              </CardHeader>
              <CardContent className="space-y-2">
                {categories.slice(1).map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full flex items-center justify-between p-2 rounded-lg text-left hover:bg-gray-50 transition-colors ${
                      selectedCategory === category.id ? "bg-blue-50 text-blue-600" : ""
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <category.icon className="h-4 w-4" />
                      <span className="text-sm">{category.name}</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {category.count}
                    </Badge>
                  </button>
                ))}
              </CardContent>
            </Card>

            {/* Popular Tags */}
            <Card>
              <CardHeader>
                <h3 className="font-semibold">Popular Tags</h3>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {popularTags.map((tag) => (
                    <Badge
                      key={tag}
                      variant="outline"
                      className="cursor-pointer hover:bg-blue-50 hover:text-blue-600 transition-colors"
                      onClick={() => setSearchQuery(tag)}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Newsletter Subscription */}
            <Card className="bg-gradient-to-br from-blue-50 to-purple-50 border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Stay Updated</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Subscribe to our newsletter and never miss the latest articles, tips, and exclusive content.
                </p>
                <div className="space-y-3">
                  <Input type="email" placeholder="Enter your email" />
                  <Button className="w-full bg-blue-600 hover:bg-blue-700">Subscribe Now</Button>
                </div>
                <p className="text-xs text-gray-500 mt-3">Join 10,000+ readers. Unsubscribe anytime.</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
