"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { ArrowRight, Search, Grid, List } from "lucide-react"
import { categories, products } from "@/lib/data/products"
import Header from "@/components/header"
import { useState } from "react"

export default function CategoriesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  const filteredCategories = categories.filter((category) =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const getCategoryProducts = (categoryId: string) => {
    return products.filter((product) => product.category === categoryId).slice(0, 3)
  }

  const getCategoryImage = (categoryId: string) => {
    const categoryProducts = products.filter((product) => product.category === categoryId)
    return categoryProducts.length > 0 ? categoryProducts[0].image : "/placeholder.svg?height=300&width=400"
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-600 to-green-700 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Shop by Categories</h1>
          <p className="text-xl opacity-90 max-w-2xl mx-auto">
            Discover our wide range of furniture categories designed to transform your living spaces
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        {/* Search and Filter Bar */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
          <div>
            <h2 className="text-2xl font-bold mb-2">Browse Categories</h2>
            <p className="text-gray-600">Find the perfect furniture for every room in your home</p>
          </div>

          <div className="flex items-center gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search categories..."
                className="pl-10 w-64"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex border rounded-md">
              <Button variant={viewMode === "grid" ? "default" : "ghost"} size="sm" onClick={() => setViewMode("grid")}>
                <Grid className="h-4 w-4" />
              </Button>
              <Button variant={viewMode === "list" ? "default" : "ghost"} size="sm" onClick={() => setViewMode("list")}>
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Categories Grid */}
        <div className={viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" : "space-y-6"}>
          {filteredCategories.map((category) => {
            const categoryProducts = getCategoryProducts(category.id)
            const categoryImage = getCategoryImage(category.id)

            return (
              <Card key={category.id} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
                {viewMode === "grid" ? (
                  <>
                    <div className="relative h-64 overflow-hidden">
                      <Image
                        src={categoryImage || "/placeholder.svg"}
                        alt={category.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300" />
                      <Badge className="absolute top-4 left-4 bg-green-600 text-white">{category.items} Items</Badge>
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-2xl font-bold mb-3 group-hover:text-green-600 transition-colors">
                        {category.name}
                      </h3>

                      {/* Subcategories */}
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-600 mb-2">Popular Types:</h4>
                        <div className="flex flex-wrap gap-2">
                          {category.subcategories.slice(0, 3).map((sub) => (
                            <Badge key={sub.id} variant="outline" className="text-xs">
                              {sub.name}
                            </Badge>
                          ))}
                          {category.subcategories.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{category.subcategories.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Featured Products Preview */}
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-600 mb-2">Featured Products:</h4>
                        <div className="grid grid-cols-3 gap-2">
                          {categoryProducts.map((product) => (
                            <div key={product.id} className="relative">
                              <Image
                                src={product.image || "/placeholder.svg"}
                                alt={product.name}
                                width={80}
                                height={80}
                                className="w-full h-16 object-cover rounded"
                              />
                            </div>
                          ))}
                        </div>
                      </div>

                      <Link href={`/products?category=${category.id}`}>
                        <Button className="w-full bg-green-600 hover:bg-green-700 group">
                          Shop {category.name}
                          <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                        </Button>
                      </Link>
                    </CardContent>
                  </>
                ) : (
                  <div className="flex gap-6 p-6">
                    <div className="relative w-48 h-32 flex-shrink-0">
                      <Image
                        src={categoryImage || "/placeholder.svg"}
                        alt={category.name}
                        fill
                        className="object-cover rounded-lg"
                      />
                      <Badge className="absolute top-2 left-2 bg-green-600 text-white text-xs">
                        {category.items} Items
                      </Badge>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold mb-3 group-hover:text-green-600 transition-colors">
                        {category.name}
                      </h3>

                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-600 mb-2">Available Types:</h4>
                        <div className="flex flex-wrap gap-2">
                          {category.subcategories.map((sub) => (
                            <Badge key={sub.id} variant="outline" className="text-xs">
                              {sub.name}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex -space-x-2">
                          {categoryProducts.map((product) => (
                            <Image
                              key={product.id}
                              src={product.image || "/placeholder.svg"}
                              alt={product.name}
                              width={40}
                              height={40}
                              className="w-10 h-10 object-cover rounded-full border-2 border-white"
                            />
                          ))}
                        </div>
                        <Link href={`/products?category=${category.id}`}>
                          <Button className="bg-green-600 hover:bg-green-700">
                            Shop Now
                            <ArrowRight className="h-4 w-4 ml-2" />
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                )}
              </Card>
            )
          })}
        </div>

        {filteredCategories.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-lg font-semibold mb-2">No categories found</h3>
            <p className="text-gray-600">Try adjusting your search terms</p>
          </div>
        )}

        {/* Popular Collections */}
        <section className="mt-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Popular Collections</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Curated collections that bring style and functionality together
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="relative overflow-hidden group cursor-pointer">
              <div className="relative h-64">
                <Image
                  src="/placeholder.svg?height=300&width=400"
                  alt="Modern Living"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute bottom-6 left-6 text-white">
                  <h3 className="text-xl font-bold mb-2">Modern Living</h3>
                  <p className="text-sm opacity-90">Contemporary furniture for modern homes</p>
                </div>
              </div>
            </Card>

            <Card className="relative overflow-hidden group cursor-pointer">
              <div className="relative h-64">
                <Image
                  src="/placeholder.svg?height=300&width=400"
                  alt="Cozy Bedroom"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute bottom-6 left-6 text-white">
                  <h3 className="text-xl font-bold mb-2">Cozy Bedroom</h3>
                  <p className="text-sm opacity-90">Create your perfect sleep sanctuary</p>
                </div>
              </div>
            </Card>

            <Card className="relative overflow-hidden group cursor-pointer">
              <div className="relative h-64">
                <Image
                  src="/placeholder.svg?height=300&width=400"
                  alt="Home Office"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute bottom-6 left-6 text-white">
                  <h3 className="text-xl font-bold mb-2">Home Office</h3>
                  <p className="text-sm opacity-90">Productive workspace essentials</p>
                </div>
              </div>
            </Card>
          </div>
        </section>
      </div>
    </div>
  )
}
