"use client"

import { useState, useMemo } from "react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Star, ShoppingCart, Heart, Clock, Flame, Tag, TrendingDown, Gift, Zap, Search } from "lucide-react"
import { products } from "@/lib/data/products"
import { useCart } from "@/lib/contexts/cart-context"
import Header from "@/components/header"

export default function DealsPage() {
  const { dispatch } = useCart()
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState("discount")
  const [selectedCategory, setSelectedCategory] = useState("all")

  // Calculate deals and discounts
  const dealsProducts = useMemo(() => {
    return products
      .map((product) => {
        const discountAmount = product.originalPrice - product.price
        const discountPercentage = Math.round((discountAmount / product.originalPrice) * 100)
        return {
          ...product,
          discountAmount,
          discountPercentage,
          savings: discountAmount,
        }
      })
      .filter((product) => product.discountPercentage > 0)
  }, [])

  // Filter and sort deals
  const filteredDeals = useMemo(() => {
    const filtered = dealsProducts.filter((product) => {
      if (searchQuery && !product.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false
      }
      if (selectedCategory !== "all" && product.category !== selectedCategory) {
        return false
      }
      return true
    })

    // Sort deals
    switch (sortBy) {
      case "discount":
        filtered.sort((a, b) => b.discountPercentage - a.discountPercentage)
        break
      case "price-low":
        filtered.sort((a, b) => a.price - b.price)
        break
      case "price-high":
        filtered.sort((a, b) => b.price - a.price)
        break
      case "savings":
        filtered.sort((a, b) => b.savings - a.savings)
        break
      default:
        break
    }

    return filtered
  }, [dealsProducts, searchQuery, selectedCategory, sortBy])

  // Flash deals (highest discounts)
  const flashDeals = dealsProducts.filter((product) => product.discountPercentage >= 40).slice(0, 4)

  // Daily deals
  const dailyDeals = dealsProducts
    .filter((product) => product.discountPercentage >= 20 && product.discountPercentage < 40)
    .slice(0, 6)

  // Clearance items
  const clearanceItems = dealsProducts.filter((product) => product.discountPercentage >= 50).slice(0, 8)

  const addToCart = (product: any) => {
    dispatch({
      type: "ADD_ITEM",
      payload: {
        id: product.id,
        name: product.name,
        price: product.price,
        originalPrice: product.originalPrice,
        image: product.image,
        category: product.category,
      },
    })
  }

  const categories = ["all", "chairs", "sofas", "lighting", "bedroom"]

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-red-600 to-orange-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Flame className="h-8 w-8" />
            <h1 className="text-4xl md:text-5xl font-bold">Hot Deals & Offers</h1>
            <Flame className="h-8 w-8" />
          </div>
          <p className="text-xl opacity-90 max-w-2xl mx-auto mb-6">
            Save big on premium furniture with our limited-time offers and flash sales
          </p>

          {/* Countdown Timer */}
          <div className="flex justify-center gap-4 mb-8">
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center">
              <div className="text-2xl font-bold">05</div>
              <div className="text-sm">Days</div>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center">
              <div className="text-2xl font-bold">12</div>
              <div className="text-sm">Hours</div>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center">
              <div className="text-2xl font-bold">30</div>
              <div className="text-sm">Mins</div>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center">
              <div className="text-2xl font-bold">25</div>
              <div className="text-sm">Secs</div>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        {/* Flash Deals Section */}
        <section className="mb-16">
          <div className="flex items-center gap-3 mb-8">
            <Zap className="h-6 w-6 text-yellow-500" />
            <h2 className="text-3xl font-bold">Flash Deals</h2>
            <Badge className="bg-red-600 text-white animate-pulse">Limited Time</Badge>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {flashDeals.map((product) => (
              <Card
                key={product.id}
                className="group hover:shadow-xl transition-all duration-300 overflow-hidden border-2 border-red-200"
              >
                <div className="relative">
                  <Link href={`/product/${product.id}`}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      width={300}
                      height={300}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </Link>
                  <Badge className="absolute top-3 left-3 bg-red-600 text-white text-lg font-bold">
                    -{product.discountPercentage}%
                  </Badge>
                  <div className="absolute top-3 right-3">
                    <Button
                      size="icon"
                      variant="secondary"
                      className="w-8 h-8 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Heart className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* Stock indicator */}
                  <div className="absolute bottom-3 left-3 right-3">
                    <div className="bg-white/90 backdrop-blur-sm rounded-lg p-2">
                      <div className="flex justify-between text-xs mb-1">
                        <span>Stock: {product.stockCount} left</span>
                        <span>{Math.round((product.stockCount / 50) * 100)}%</span>
                      </div>
                      <Progress value={(product.stockCount / 50) * 100} className="h-1" />
                    </div>
                  </div>
                </div>

                <CardContent className="p-4">
                  <div className="flex items-center gap-1 mb-2">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-3 w-3 ${
                          i < Math.floor(product.rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                        }`}
                      />
                    ))}
                    <span className="text-xs text-gray-600 ml-1">({product.reviewCount})</span>
                  </div>

                  <Link href={`/product/${product.id}`}>
                    <h3 className="font-semibold mb-2 line-clamp-2 hover:text-red-600 transition-colors">
                      {product.name}
                    </h3>
                  </Link>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold text-red-600">₹{product.price.toLocaleString('en-IN')}</span>
                      <span className="text-sm text-gray-500 line-through">₹{product.originalPrice.toLocaleString('en-IN')}</span>
                    </div>
                    <div className="text-xs text-green-600 font-medium">Save ₹{product.savings.toLocaleString('en-IN')}</div>
                  </div>

                  <Button
                    size="sm"
                    onClick={() => addToCart(product)}
                    className="w-full mt-3 bg-red-600 hover:bg-red-700"
                  >
                    <ShoppingCart className="h-4 w-4 mr-1" />
                    Add to Cart
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Deals Tabs */}
        <Tabs defaultValue="all-deals" className="space-y-8">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
            <TabsList className="grid w-full lg:w-auto grid-cols-4">
              <TabsTrigger value="all-deals" className="flex items-center gap-2">
                <Tag className="h-4 w-4" />
                All Deals
              </TabsTrigger>
              <TabsTrigger value="daily-deals" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Daily Deals
              </TabsTrigger>
              <TabsTrigger value="clearance" className="flex items-center gap-2">
                <TrendingDown className="h-4 w-4" />
                Clearance
              </TabsTrigger>
              <TabsTrigger value="bundles" className="flex items-center gap-2">
                <Gift className="h-4 w-4" />
                Bundles
              </TabsTrigger>
            </TabsList>

            {/* Filters */}
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search deals..."
                  className="pl-10 w-64"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="chairs">Chairs</SelectItem>
                  <SelectItem value="sofas">Sofas</SelectItem>
                  <SelectItem value="lighting">Lighting</SelectItem>
                  <SelectItem value="bedroom">Bedroom</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="discount">Highest Discount</SelectItem>
                  <SelectItem value="savings">Biggest Savings</SelectItem>
                  <SelectItem value="price-low">Price: Low to High</SelectItem>
                  <SelectItem value="price-high">Price: High to Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <TabsContent value="all-deals">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredDeals.map((product) => (
                <Card key={product.id} className="group hover:shadow-lg transition-shadow overflow-hidden">
                  <div className="relative">
                    <Link href={`/product/${product.id}`}>
                      <Image
                        src={product.image || "/placeholder.svg"}
                        alt={product.name}
                        width={300}
                        height={300}
                        className="w-full h-48 object-cover"
                      />
                    </Link>
                    <Badge className="absolute top-3 left-3 bg-green-600 text-white">
                      -{product.discountPercentage}%
                    </Badge>
                    <Button
                      size="icon"
                      variant="secondary"
                      className="absolute top-3 right-3 w-8 h-8 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Heart className="w-4 h-4" />
                    </Button>
                  </div>

                  <CardContent className="p-4">
                    <div className="flex items-center gap-1 mb-2">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-3 w-3 ${
                            i < Math.floor(product.rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                          }`}
                        />
                      ))}
                      <span className="text-xs text-gray-600 ml-1">({product.reviewCount})</span>
                    </div>

                    <Link href={`/product/${product.id}`}>
                      <h3 className="font-semibold mb-2 line-clamp-2 hover:text-green-600">{product.name}</h3>
                    </Link>

                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <span className="text-lg font-bold">₹{product.price.toLocaleString('en-IN')}</span>
                        <span className="text-sm text-gray-500 line-through ml-2">₹{product.originalPrice.toLocaleString('en-IN')}</span>
                      </div>
                      <div className="text-xs text-green-600 font-medium">Save ₹{product.savings.toLocaleString('en-IN')}</div>
                    </div>

                    <Button
                      size="sm"
                      onClick={() => addToCart(product)}
                      className="w-full bg-green-600 hover:bg-green-700"
                    >
                      <ShoppingCart className="h-4 w-4 mr-1" />
                      Add to Cart
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="daily-deals">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {dailyDeals.map((product) => (
                <Card key={product.id} className="group hover:shadow-lg transition-shadow overflow-hidden">
                  <div className="relative">
                    <Link href={`/product/${product.id}`}>
                      <Image
                        src={product.image || "/placeholder.svg"}
                        alt={product.name}
                        width={300}
                        height={300}
                        className="w-full h-48 object-cover"
                      />
                    </Link>
                    <Badge className="absolute top-3 left-3 bg-blue-600 text-white">Daily Deal</Badge>
                    <Badge className="absolute top-3 right-3 bg-orange-600 text-white">
                      -{product.discountPercentage}%
                    </Badge>
                  </div>

                  <CardContent className="p-4">
                    <Link href={`/product/${product.id}`}>
                      <h3 className="font-semibold mb-2 hover:text-blue-600">{product.name}</h3>
                    </Link>

                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <span className="text-lg font-bold">₹{product.price.toLocaleString('en-IN')}</span>
                        <span className="text-sm text-gray-500 line-through ml-2">₹{product.originalPrice.toLocaleString('en-IN')}</span>
                      </div>
                    </div>

                    <Button
                      size="sm"
                      onClick={() => addToCart(product)}
                      className="w-full bg-blue-600 hover:bg-blue-700"
                    >
                      <ShoppingCart className="h-4 w-4 mr-1" />
                      Add to Cart
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="clearance">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {clearanceItems.map((product) => (
                <Card
                  key={product.id}
                  className="group hover:shadow-lg transition-shadow overflow-hidden border-2 border-orange-200"
                >
                  <div className="relative">
                    <Link href={`/product/${product.id}`}>
                      <Image
                        src={product.image || "/placeholder.svg"}
                        alt={product.name}
                        width={300}
                        height={300}
                        className="w-full h-48 object-cover"
                      />
                    </Link>
                    <Badge className="absolute top-3 left-3 bg-orange-600 text-white text-lg font-bold">
                      CLEARANCE
                    </Badge>
                    <Badge className="absolute bottom-3 right-3 bg-red-600 text-white">
                      -{product.discountPercentage}%
                    </Badge>
                  </div>

                  <CardContent className="p-4">
                    <Link href={`/product/${product.id}`}>
                      <h3 className="font-semibold mb-2 hover:text-orange-600">{product.name}</h3>
                    </Link>

                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-orange-600">₹{product.price.toLocaleString('en-IN')}</span>
                        <span className="text-sm text-gray-500 line-through">₹{product.originalPrice.toLocaleString('en-IN')}</span>
                      </div>
                      <div className="text-xs text-green-600 font-medium">
                        Final Sale - Save ₹{product.savings.toLocaleString('en-IN')}
                      </div>
                    </div>

                    <Button
                      size="sm"
                      onClick={() => addToCart(product)}
                      className="w-full mt-3 bg-orange-600 hover:bg-orange-700"
                    >
                      <ShoppingCart className="h-4 w-4 mr-1" />
                      Add to Cart
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="bundles">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Bundle deals */}
              <Card className="overflow-hidden">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Gift className="h-5 w-5 text-purple-600" />
                    Living Room Bundle
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <Image
                      src="/placeholder.svg?height=100&width=100"
                      alt="Sofa"
                      width={100}
                      height={100}
                      className="rounded"
                    />
                    <Image
                      src="/placeholder.svg?height=100&width=100"
                      alt="Chair"
                      width={100}
                      height={100}
                      className="rounded"
                    />
                    <Image
                      src="/placeholder.svg?height=100&width=100"
                      alt="Table"
                      width={100}
                      height={100}
                      className="rounded"
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Individual Price:</span>
                      <span className="line-through text-gray-500">₹2,07,417</span>
                    </div>
                    <div className="flex justify-between font-bold text-lg">
                      <span>Bundle Price:</span>
                      <span className="text-purple-600">₹1,57,617</span>
                    </div>
                    <div className="text-green-600 font-medium">Save ₹49,800!</div>
                  </div>
                  <Button className="w-full mt-4 bg-purple-600 hover:bg-purple-700">
                    <Gift className="h-4 w-4 mr-2" />
                    Buy Bundle
                  </Button>
                </CardContent>
              </Card>

              <Card className="overflow-hidden">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Gift className="h-5 w-5 text-purple-600" />
                    Bedroom Essentials
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <Image
                      src="/placeholder.svg?height=100&width=100"
                      alt="Bed"
                      width={100}
                      height={100}
                      className="rounded"
                    />
                    <Image
                      src="/placeholder.svg?height=100&width=100"
                      alt="Nightstand"
                      width={100}
                      height={100}
                      className="rounded"
                    />
                    <Image
                      src="/placeholder.svg?height=100&width=100"
                      alt="Dresser"
                      width={100}
                      height={100}
                      className="rounded"
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Individual Price:</span>
                      <span className="line-through text-gray-500">₹1,49,317</span>
                    </div>
                    <div className="flex justify-between font-bold text-lg">
                      <span>Bundle Price:</span>
                      <span className="text-purple-600">₹1,16,117</span>
                    </div>
                    <div className="text-green-600 font-medium">Save ₹33,200!</div>
                  </div>
                  <Button className="w-full mt-4 bg-purple-600 hover:bg-purple-700">
                    <Gift className="h-4 w-4 mr-2" />
                    Buy Bundle
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Newsletter Signup */}
        <section className="mt-16">
          <Card className="bg-gradient-to-r from-green-600 to-blue-600 text-white">
            <CardContent className="p-8 text-center">
              <h3 className="text-2xl font-bold mb-4">Never Miss a Deal!</h3>
              <p className="mb-6 opacity-90">
                Subscribe to our newsletter and be the first to know about exclusive offers and flash sales.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input type="email" placeholder="Enter your email" className="bg-white text-gray-900" />
                <Button className="bg-white text-green-600 hover:bg-gray-100">Subscribe</Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}
