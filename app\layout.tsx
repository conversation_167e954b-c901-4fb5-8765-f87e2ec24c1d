import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { CartProvider } from "@/lib/contexts/cart-context"
import { AuthProvider } from "@/lib/contexts/auth-context"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "FurnitureStore - Modern Furniture Collection",
  description:
    "Explore our modern furniture collection with premium chairs, sofas, lighting, and bedroom furniture. Free shipping on orders above $180.",
  keywords: "furniture, modern furniture, chairs, sofas, lighting, bedroom furniture, home decor",
  authors: [{ name: "FurnitureStore Team" }],
  openGraph: {
    title: "FurnitureStore - Modern Furniture Collection",
    description: "Explore our modern furniture collection with premium quality and stylish designs.",
    url: "https://furniturestore.com",
    siteName: "FurnitureStore",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "FurnitureStore - Modern Furniture",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  robots: {
    index: true,
    follow: true,
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <CartProvider>{children}</CartProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
