"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, ArrowRight, ArrowLeft, Heart, Eye, RotateCcw, Truck, CreditCard, Headphones } from "lucide-react"
import { products } from "@/lib/data/products"
import { useCart } from "@/lib/contexts/cart-context"
import Header from "@/components/header"

export default function FurnitureHomePage() {
  const { dispatch } = useCart()

  const featuredProducts = products.filter((p) => p.isFeatured).slice(0, 4)

  const categories = [
    {
      name: "Chairs",
      items: "1500+",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit",
      subcategories: [
        "Gaming Chair",
        "Lounge Chair",
        "Folding Chair",
        "Dining Chair",
        "Office Chair",
        "Armchair",
        "Bar Stool",
        "Club Chair",
      ],
      image: "/placeholder.svg?height=400&width=300",
    },
    {
      name: "Sofa",
      items: "750+",
      subcategories: ["Reception Sofa", "Sectional Sofa", "Armless Sofa", "Curved Sofa"],
      image: "/placeholder.svg?height=300&width=400",
    },
    {
      name: "Lighting",
      items: "450+",
      subcategories: ["Table Lights", "Floor Lights", "Ceiling Lights", "Wall Lights"],
      image: "/placeholder.svg?height=300&width=300",
    },
  ]

  const addToCart = (product: (typeof products)[0]) => {
    dispatch({
      type: "ADD_ITEM",
      payload: {
        id: product.id,
        name: product.name,
        price: product.price,
        originalPrice: product.originalPrice,
        image: product.image,
        category: product.category,
      },
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Hero Section */}
      <section className="bg-gray-100 py-16">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-5xl font-bold text-gray-900 mb-6">
                Explore Our Modern
                <br />
                <span className="text-green-600">Furniture Collection</span>
              </h1>
              <p className="text-gray-600 mb-8 text-lg">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                dolore
              </p>
              <div className="flex gap-4 mb-8">
                <Link href="/products">
                  <Button className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-full">
                    Shop Now →
                  </Button>
                </Link>
                <Link href="/products">
                  <Button variant="outline" className="px-8 py-3 rounded-full">
                    View All Products
                  </Button>
                </Link>
              </div>

              {/* Customer Reviews */}
              <div className="flex items-center gap-4">
                <div className="flex -space-x-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="w-10 h-10 rounded-full bg-gray-300 border-2 border-white overflow-hidden">
                      <Image src="/placeholder.svg?height=40&width=40" alt="Customer" width={40} height={40} />
                    </div>
                  ))}
                  <div className="w-10 h-10 rounded-full bg-green-600 border-2 border-white flex items-center justify-center">
                    <span className="text-white text-sm font-bold">+</span>
                  </div>
                </div>
                <div>
                  <div className="flex items-center gap-1">
                    <span className="font-bold text-lg">4.9 Ratings</span>
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  </div>
                  <p className="text-sm text-gray-600">Trusted by 50k+ Customers</p>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="grid grid-cols-2 gap-4">
                <Card className="relative overflow-hidden">
                  <Image
                    src="/images/hero/living-room.jpg"
                    alt="Living Room"
                    width={400}
                    height={300}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-4 right-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm">
                    $1,200
                  </div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="font-semibold">Living Room</h3>
                    <p className="text-sm opacity-90">2,500+ Items</p>
                  </div>
                  <div className="absolute bottom-4 right-4 flex gap-2">
                    <Button size="icon" className="bg-green-600 hover:bg-green-700 rounded-full w-8 h-8">
                      <span className="text-white">✕</span>
                    </Button>
                  </div>
                </Card>

                <Card className="relative overflow-hidden">
                  <Image
                    src="/images/hero/bedroom.jpg"
                    alt="Bed Room"
                    width={400}
                    height={300}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="font-semibold">Bed Room</h3>
                    <p className="text-sm opacity-90">1,500+ Items</p>
                  </div>
                </Card>
              </div>

              {/* Navigation Arrows */}
              <div className="absolute -left-4 top-1/2 transform -translate-y-1/2 flex gap-2">
                <Button size="icon" className="bg-green-600 hover:bg-green-700 rounded-full">
                  <ArrowLeft className="w-4 h-4 text-white" />
                </Button>
                <Button size="icon" className="bg-yellow-500 hover:bg-yellow-600 rounded-full">
                  <ArrowRight className="w-4 h-4 text-white" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Badges */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Truck className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Free Shipping</h3>
                <p className="text-sm text-gray-600">Free shipping for order above $180</p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <CreditCard className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Flexible Payment</h3>
                <p className="text-sm text-gray-600">Multiple secure payment options</p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Headphones className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">24×7 Support</h3>
                <p className="text-sm text-gray-600">We support online all days.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Chairs Category */}
            <div className="lg:col-span-1">
              <div className="mb-4">
                <span className="text-yellow-600 font-medium">{categories[0].items} Items</span>
                <h2 className="text-3xl font-bold text-gray-900 mb-4">{categories[0].name}</h2>
                <p className="text-gray-600 mb-6">{categories[0].description}</p>
              </div>

              <div className="space-y-2 mb-6">
                {categories[0].subcategories.map((sub, index) => (
                  <div key={index} className="flex items-center justify-between py-2 border-b border-gray-200">
                    <span className="text-gray-700">{sub}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Chair Image */}
            <div className="lg:col-span-1 flex justify-center">
              <div className="relative">
                <Image
                  src="/images/hero/modern-chair.jpg"
                  alt="Modern Chair"
                  width={300}
                  height={400}
                  className="object-cover rounded-lg"
                />
              </div>
            </div>

            {/* Sofa and Lighting */}
            <div className="lg:col-span-1 space-y-8">
              {/* Sofa Section */}
              <div>
                <span className="text-yellow-600 font-medium">{categories[1].items} Items</span>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{categories[1].name}</h3>
                <div className="space-y-2 mb-4">
                  {categories[1].subcategories.map((sub, index) => (
                    <div key={index} className="text-gray-700">
                      {sub}
                    </div>
                  ))}
                </div>
                <Image
                  src="/placeholder.svg?height=200&width=300"
                  alt="Modern Sofa"
                  width={300}
                  height={200}
                  className="w-full object-cover rounded-lg mb-6"
                />
              </div>

              {/* Lighting Section */}
              <div>
                <span className="text-yellow-600 font-medium">{categories[2].items} Items</span>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{categories[2].name}</h3>
                <div className="space-y-2 mb-4">
                  {categories[2].subcategories.map((sub, index) => (
                    <div key={index} className="text-gray-700">
                      {sub}
                    </div>
                  ))}
                </div>
                <Image
                  src="/placeholder.svg?height=200&width=300"
                  alt="Modern Lighting"
                  width={300}
                  height={200}
                  className="w-full object-cover rounded-lg"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <p className="text-gray-600 mb-2">— Our Products</p>
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Our Products Collections</h2>

            <div className="flex justify-center gap-4 mb-8">
              <Button variant="outline" className="rounded-full">
                All Products
              </Button>
              <Button className="bg-green-600 hover:bg-green-700 text-white rounded-full">Latest Products</Button>
              <Button variant="outline" className="rounded-full">
                Best Sellers
              </Button>
              <Button variant="outline" className="rounded-full">
                Featured Products
              </Button>
            </div>
          </div>

          {/* Countdown Timer */}
          <div className="flex justify-center mb-8">
            <div className="flex gap-4">
              <div className="bg-yellow-500 text-white px-3 py-2 rounded text-center">
                <div className="font-bold">05</div>
                <div className="text-xs">Days</div>
              </div>
              <div className="bg-yellow-500 text-white px-3 py-2 rounded text-center">
                <div className="font-bold">12</div>
                <div className="text-xs">Hours</div>
              </div>
              <div className="bg-yellow-500 text-white px-3 py-2 rounded text-center">
                <div className="font-bold">30</div>
                <div className="text-xs">Mins</div>
              </div>
              <div className="bg-yellow-500 text-white px-3 py-2 rounded text-center">
                <div className="font-bold">25</div>
                <div className="text-xs">Secs</div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProducts.map((product) => (
              <Card key={product.id} className="group hover:shadow-lg transition-shadow overflow-hidden">
                <div className="relative">
                  <Link href={`/product/${product.id}`}>
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      width={300}
                      height={300}
                      className="w-full h-64 object-cover"
                    />
                  </Link>
                  <Badge className="absolute top-3 left-3 bg-green-600 text-white">{product.discount}</Badge>
                  <div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button size="icon" variant="secondary" className="w-8 h-8">
                      <Heart className="w-4 h-4" />
                    </Button>
                    <Button size="icon" variant="secondary" className="w-8 h-8">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button size="icon" variant="secondary" className="w-8 h-8">
                      <RotateCcw className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <CardContent className="p-4">
                  <div className="flex items-center gap-1 mb-2">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm font-medium">{product.rating}</span>
                    <span className="text-sm text-gray-500 ml-2">{product.category}</span>
                  </div>
                  <Link href={`/product/${product.id}`}>
                    <h3 className="font-semibold text-gray-900 mb-2 hover:text-green-600">{product.name}</h3>
                  </Link>
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-lg font-bold text-gray-900">${product.price.toFixed(2)}</span>
                      <span className="text-sm text-gray-500 line-through ml-2">
                        ${product.originalPrice.toFixed(2)}
                      </span>
                    </div>
                    <Button size="sm" onClick={() => addToCart(product)} className="bg-green-600 hover:bg-green-700">
                      Add
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Flash Sale Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              <span className="text-green-600">Flash Sale!</span>
            </h2>
          </div>

          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div className="relative">
              <Image
                src="/placeholder.svg?height=400&width=500"
                alt="Flash Sale Furniture"
                width={500}
                height={400}
                className="w-full object-cover rounded-lg"
              />
            </div>
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-4">Up to 70% Off</h3>
              <p className="text-gray-600 mb-6">
                Don't miss out on our biggest sale of the year! Get amazing discounts on premium furniture.
              </p>
              <Link href="/products">
                <Button className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-full">
                  Shop Sale Items
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">🌲</span>
                </div>
                <span className="font-bold text-lg">FurnitureStore</span>
              </div>
              <p className="text-gray-400 mb-4">Your trusted partner for modern and stylish furniture solutions.</p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/about" className="hover:text-white">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="/products" className="hover:text-white">
                    Products
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="hover:text-white">
                    Contact
                  </Link>
                </li>
                <li>
                  <Link href="/blog" className="hover:text-white">
                    Blog
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Categories</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/products?category=chairs" className="hover:text-white">
                    Chairs
                  </Link>
                </li>
                <li>
                  <Link href="/products?category=sofas" className="hover:text-white">
                    Sofas
                  </Link>
                </li>
                <li>
                  <Link href="/products?category=lighting" className="hover:text-white">
                    Lighting
                  </Link>
                </li>
                <li>
                  <Link href="/products?category=bedroom" className="hover:text-white">
                    Bedroom
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Contact Info</h4>
              <div className="space-y-2 text-gray-400">
                <p>📧 <EMAIL></p>
                <p>📞 +1 (555) 123-4567</p>
                <p>📍 123 Furniture St, Design City</p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 FurnitureStore. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
