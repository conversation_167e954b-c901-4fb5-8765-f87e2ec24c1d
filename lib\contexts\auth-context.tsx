"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"

interface User {
  id: number
  email: string
  firstName: string
  lastName: string
  phone?: string
  addresses: Address[]
  orders: Order[]
}

interface Address {
  id: number
  type: "home" | "work" | "other"
  firstName: string
  lastName: string
  address: string
  apartment?: string
  city: string
  state: string
  zipCode: string
  country: string
  isDefault: boolean
}

interface Order {
  id: string
  date: string
  status: "processing" | "shipped" | "delivered" | "cancelled"
  total: number
  items: any[]
}

interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
}

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<boolean>
  register: (userData: {
    email: string
    password: string
    firstName: string
    lastName: string
  }) => Promise<boolean>
  logout: () => void
  updateUser: (userData: Partial<User>) => void
}

const AuthContext = createContext<AuthContextType | null>(null)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
  })

  useEffect(() => {
    // Check for existing session
    const savedUser = localStorage.getItem("furniture-user")
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser)
        setState({
          user,
          isLoading: false,
          isAuthenticated: true,
        })
      } catch (error) {
        console.error("Error loading user from localStorage:", error)
        setState((prev) => ({ ...prev, isLoading: false }))
      }
    } else {
      setState((prev) => ({ ...prev, isLoading: false }))
    }
  }, [])

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Mock user data
      const user: User = {
        id: 1,
        email,
        firstName: "John",
        lastName: "Doe",
        phone: "+****************",
        addresses: [
          {
            id: 1,
            type: "home",
            firstName: "John",
            lastName: "Doe",
            address: "123 Main Street",
            apartment: "Apt 4B",
            city: "New York",
            state: "NY",
            zipCode: "10001",
            country: "United States",
            isDefault: true,
          },
        ],
        orders: [
          {
            id: "ORD-001",
            date: "2024-01-15",
            status: "delivered",
            total: 299.99,
            items: [],
          },
        ],
      }

      localStorage.setItem("furniture-user", JSON.stringify(user))
      setState({
        user,
        isLoading: false,
        isAuthenticated: true,
      })

      return true
    } catch (error) {
      console.error("Login error:", error)
      return false
    }
  }

  const register = async (userData: {
    email: string
    password: string
    firstName: string
    lastName: string
  }): Promise<boolean> => {
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const user: User = {
        id: Date.now(),
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        addresses: [],
        orders: [],
      }

      localStorage.setItem("furniture-user", JSON.stringify(user))
      setState({
        user,
        isLoading: false,
        isAuthenticated: true,
      })

      return true
    } catch (error) {
      console.error("Registration error:", error)
      return false
    }
  }

  const logout = () => {
    localStorage.removeItem("furniture-user")
    setState({
      user: null,
      isLoading: false,
      isAuthenticated: false,
    })
  }

  const updateUser = (userData: Partial<User>) => {
    if (!state.user) return

    const updatedUser = { ...state.user, ...userData }
    localStorage.setItem("furniture-user", JSON.stringify(updatedUser))
    setState((prev) => ({
      ...prev,
      user: updatedUser,
    }))
  }

  return (
    <AuthContext.Provider
      value={{
        ...state,
        login,
        register,
        logout,
        updateUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
